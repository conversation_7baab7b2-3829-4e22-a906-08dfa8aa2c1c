"use client";

import { useState } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { motion, AnimatePresence } from "framer-motion";
import {
  LayoutDashboard,
  User,
  Settings,
  LogOut,
  ChevronLeft,
  ChevronRight,
  CreditCard,
  Bell,
  HelpCircle,
  Zap,
  Menu,
  X
} from "lucide-react";
import { cn } from "@/lib/utils";
import { authClient } from "@/lib/auth-client";
import { useRouter } from "next/navigation";

interface NavItem {
  path: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string;
  description?: string;
}

const navItems: NavItem[] = [
  {
    path: "/dashboard",
    label: "Dashboard",
    icon: LayoutDashboard,
    description: "Overview and analytics"
  },
  {
    path: "/billing",
    label: "Billing",
    icon: CreditCard,
    description: "Manage subscriptions"
  },
  {
    path: "/account",
    label: "Account",
    icon: User,
    description: "Profile settings"
  },
];

const bottomNavItems: NavItem[] = [
  {
    path: "/settings",
    label: "Settings",
    icon: Settings,
    description: "App preferences"
  },
  {
    path: "/help",
    label: "Help & Support",
    icon: HelpCircle,
    description: "Get assistance"
  },
];

interface SidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
}

export function Sidebar({ isCollapsed, onToggle }: SidebarProps) {
  const pathname = usePathname();
  const router = useRouter();
  const [isSigningOut, setIsSigningOut] = useState(false);

  const handleSignOut = async () => {
    setIsSigningOut(true);
    try {
      await authClient.signOut({
        fetchOptions: {
          onSuccess: () => {
            router.push("/");
          },
          onError: (error) => {
            console.error("Sign out error:", error);
            setIsSigningOut(false);
          }
        }
      });
    } catch (error) {
      console.error("Sign out error:", error);
      setIsSigningOut(false);
    }
  };

  return (
    <div
      className={cn(
        "relative h-full bg-white dark:bg-neutral-900 border-r border-neutral-200 dark:border-neutral-800",
        "flex flex-col shadow-sm z-30 transition-all duration-300 ease-in-out",
        isCollapsed ? "w-16" : "w-64"
      )}
    >
      {/* Header */}
      <div className="flex-shrink-0 border-b border-neutral-200 dark:border-neutral-800 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3 min-w-0">
            <div className="w-8 h-8 bg-gradient-to-br from-[#ffbe98] to-[#ff9a56] rounded-lg flex items-center justify-center shadow-lg flex-shrink-0">
              <Zap className="w-4 h-4 text-white" />
            </div>
            {!isCollapsed && (
              <div className="min-w-0">
                <h1 className="text-lg font-bold text-neutral-900 dark:text-white tracking-tight">
                  Creem
                </h1>
                <p className="text-xs text-neutral-500 dark:text-neutral-400 font-medium">
                  SaaS Platform
                </p>
              </div>
            )}
          </div>

          {!isCollapsed && (
            <button
              onClick={onToggle}
              className="p-2 rounded-lg hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-all duration-200"
              aria-label="Collapse sidebar"
            >
              <ChevronLeft className="w-4 h-4 text-neutral-600 dark:text-neutral-400" />
            </button>
          )}
        </div>

        {isCollapsed && (
          <button
            onClick={onToggle}
            className="w-full mt-2 p-2 rounded-lg hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-all duration-200 flex justify-center"
            aria-label="Expand sidebar"
          >
            <ChevronRight className="w-4 h-4 text-neutral-600 dark:text-neutral-400" />
          </button>
        )}
      </div>

      {/* Navigation */}
      <div className="flex-1 overflow-y-auto py-2">
        <nav className="space-y-1 px-2">
          {navItems.map((item) => {
            const isActive = pathname === item.path;
            const Icon = item.icon;

            return (
              <Link
                key={item.path}
                href={item.path}
                className={cn(
                  "flex items-center rounded-lg transition-all duration-200 group relative",
                  isCollapsed ? "justify-center p-3" : "gap-3 px-3 py-2.5",
                  isActive
                    ? "bg-[#ffbe98]/10 text-[#ffbe98] dark:bg-[#ffbe98]/20"
                    : "text-neutral-600 dark:text-neutral-400 hover:bg-neutral-100 dark:hover:bg-neutral-800 hover:text-neutral-900 dark:hover:text-neutral-200"
                )}
              >
                <Icon className={cn(
                  "w-5 h-5 flex-shrink-0",
                  isActive && "text-[#ffbe98]"
                )} />

                {!isCollapsed && (
                  <div className="flex items-center justify-between flex-1 min-w-0">
                    <span className="font-medium text-sm truncate">{item.label}</span>
                    {item.badge && (
                      <span className="px-2 py-0.5 text-xs bg-gradient-to-r from-[#ffbe98] to-[#ff9a56] text-white rounded-full">
                        {item.badge}
                      </span>
                    )}
                  </div>
                )}

                {/* Tooltip for collapsed state */}
                {isCollapsed && (
                  <div className="absolute left-full ml-2 px-2 py-1 bg-neutral-900 dark:bg-neutral-700 text-white text-sm rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                    {item.label}
                    {item.badge && (
                      <span className="ml-2 px-1.5 py-0.5 text-xs bg-[#ffbe98] text-white rounded">
                        {item.badge}
                      </span>
                    )}
                  </div>
                )}
              </Link>
            );
          })}
        </nav>

        {/* Divider */}
        <div className="mx-2 my-4 border-t border-neutral-200 dark:border-neutral-800" />

        {/* Bottom Navigation */}
        <nav className="space-y-1 px-2">
          {bottomNavItems.map((item) => {
            const isActive = pathname === item.path;
            const Icon = item.icon;

            return (
              <Link
                key={item.path}
                href={item.path}
                className={cn(
                  "flex items-center rounded-lg transition-all duration-200 group relative",
                  isCollapsed ? "justify-center p-3" : "gap-3 px-3 py-2.5",
                  isActive
                    ? "bg-[#ffbe98]/10 text-[#ffbe98] dark:bg-[#ffbe98]/20"
                    : "text-neutral-600 dark:text-neutral-400 hover:bg-neutral-100 dark:hover:bg-neutral-800 hover:text-neutral-900 dark:hover:text-neutral-200"
                )}
              >
                <Icon className={cn(
                  "w-5 h-5 flex-shrink-0",
                  isActive && "text-[#ffbe98]"
                )} />

                {!isCollapsed && (
                  <span className="font-medium text-sm truncate">{item.label}</span>
                )}

                {/* Tooltip for collapsed state */}
                {isCollapsed && (
                  <div className="absolute left-full ml-2 px-2 py-1 bg-neutral-900 dark:bg-neutral-700 text-white text-sm rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                    {item.label}
                  </div>
                )}
              </Link>
            );
          })}
        </nav>
      </div>

      {/* Sign Out Button */}
      <div className="flex-shrink-0 p-2 border-t border-neutral-200 dark:border-neutral-800">
        <button
          onClick={handleSignOut}
          disabled={isSigningOut}
          className={cn(
            "flex items-center rounded-lg transition-all duration-200 group relative w-full",
            "text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-950/20",
            isCollapsed ? "justify-center p-3" : "gap-3 px-3 py-2.5",
            isSigningOut && "opacity-50 cursor-not-allowed"
          )}
        >
          <LogOut className="w-5 h-5 flex-shrink-0" />

          {!isCollapsed && (
            <span className="font-medium text-sm">
              {isSigningOut ? "Signing out..." : "Sign out"}
            </span>
          )}

          {/* Tooltip for collapsed state */}
          {isCollapsed && (
            <div className="absolute left-full ml-2 px-2 py-1 bg-neutral-900 dark:bg-neutral-700 text-white text-sm rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
              {isSigningOut ? "Signing out..." : "Sign out"}
            </div>
          )}
        </button>
      </div>
    </div>
  );
}
